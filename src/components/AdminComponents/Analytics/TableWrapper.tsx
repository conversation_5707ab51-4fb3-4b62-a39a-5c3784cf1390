import React, { useState, useEffect, useMemo, cloneElement } from "react";
import Image from "next/image";

interface TableWrapperProps {
  children: React.ReactElement<{ data: any[] }>;
  data: any[];
  setData?: (data: any[]) => void;
  searchPlaceholder?: string;
  sortableColumns: {
    key: string;
    label: string;
  }[];
  filterOptions?: {
    key: string;
    label: string;
    options: string[];
  }[];
}

const TableWrapper: React.FC<TableWrapperProps> = ({
  children,
  data,
  setData,
  searchPlaceholder = "Search by office name or city...",
  sortableColumns,
  filterOptions = [],
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState<{
    key: string | null;
    direction: "asc" | "desc" | null;
  }>({ key: null, direction: null });
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const filteredAndSortedData = useMemo(() => {
    let result = [...data];

    if (searchTerm) {
      result = result.filter((item) =>
        Object.values(item).some((val) =>
          String(val).toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        result = result.filter((item) => String(item[key]) === value);
      }
    });

    if (sortConfig.key && sortConfig.direction) {
      result.sort((a, b) => {
        const aVal = a[sortConfig.key!];
        const bVal = b[sortConfig.key!];

        if (typeof aVal === "string" && typeof bVal === "string") {
          return sortConfig.direction === "asc"
            ? aVal.localeCompare(bVal)
            : bVal.localeCompare(aVal);
        }

        if (typeof aVal === "number" && typeof bVal === "number") {
          return sortConfig.direction === "asc" ? aVal - bVal : bVal - aVal;
        }

        return 0;
      });
    }

    return result;
  }, [data, searchTerm, sortConfig, filters]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    return filteredAndSortedData.slice(startIndex, endIndex);
  }, [filteredAndSortedData, currentPage, rowsPerPage]);

  const totalPages = Math.ceil(filteredAndSortedData.length / rowsPerPage);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, filters, rowsPerPage]);

  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(Math.max(1, totalPages));
    }
  }, [currentPage, totalPages]);

  const handleSort = (key: string) => {
    setSortConfig((prevConfig) => ({
      key,
      direction:
        prevConfig.key === key && prevConfig.direction === "asc"
          ? "desc"
          : "asc",
    }));
  };

  const tableContent = useMemo(() => {
    if (!children) return null;
    if (React.isValidElement(children)) {
      return React.cloneElement(children, {
        ...children.props,
        data: paginatedData,
      });
    }
    return null;
  }, [children, paginatedData]);

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        {/* Search */}
        <div className="relative w-full md:w-[300px]">
          <input
            type="text"
            placeholder={searchPlaceholder}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 border border-[#DEDEDE] rounded-lg pl-10 focus:outline-none focus:border-[#20523C]"
          />
          <Image
            src="/assets/search-normal.svg"
            alt="search"
            width={20}
            height={20}
            className="absolute left-3 top-1/2 transform -translate-y-1/2"
          />
        </div>
      </div>

      <div className="relative overflow-x-auto rounded-lg border border-[#DEDEDE]">
        <div className="group/table">
          <div className="absolute inset-0 pointer-events-none transition-colors duration-150 ease-in-out group-hover/table:bg-black/[0.01]"></div>
          {tableContent}
        </div>
      </div>

      {/* Pagination */}
      <div className="flex flex-col md:flex-row justify-between items-center gap-4 mt-4 px-4">
        <div className="flex items-center gap-4">
          <div className="text-[14px] text-[#585858]">
            Show
            <select
              value={rowsPerPage}
              onChange={(e) => setRowsPerPage(Number(e.target.value))}
              className="mx-2 border border-[#DEDEDE] rounded px-2 py-1 focus:outline-none focus:border-[#20523C]"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
            entries
          </div>
          <div className="text-[14px] text-[#585858]">
            Showing{" "}
            {Math.min(
              (currentPage - 1) * rowsPerPage + 1,
              filteredAndSortedData.length
            )}{" "}
            to{" "}
            {Math.min(currentPage * rowsPerPage, filteredAndSortedData.length)}{" "}
            of {filteredAndSortedData.length} entries
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
            className={`px-3 py-1 rounded cursor-pointer border ${
              currentPage === 1
                ? "border-[#DEDEDE] text-[#DEDEDE] cursor-not-allowed"
                : "border-[#20523C] text-[#20523C] hover:bg-[#20523C] hover:text-white"
            }`}
          >
            Previous
          </button>
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => setCurrentPage(page)}
              className={`w-8 h-8 rounded cursor-pointer ${
                currentPage === page
                  ? "bg-[#20523C] text-white"
                  : "border border-[#DEDEDE] text-[#585858] hover:border-[#20523C]"
              }`}
            >
              {page}
            </button>
          ))}
          <button
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
            className={`px-3 py-1 rounded cursor-pointer border ${
              currentPage === totalPages
                ? "border-[#DEDEDE] text-[#DEDEDE] cursor-not-allowed"
                : "border-[#20523C] text-[#20523C] hover:bg-[#20523C] hover:text-white"
            }`}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default TableWrapper;
