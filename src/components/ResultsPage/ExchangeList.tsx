import React, { useState, useEffect } from "react";
import ExchangeC<PERSON> from "./ExchangeCard";
import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { ExchangeOffice, WorkingHoursObject } from "./types";
import { truncateName } from "./ExchangeCard";
import * as api from "../../services/api";

// Helper function to convert working hours to string
const formatWorkingHours = (hours: string | WorkingHoursObject): string => {
  if (typeof hours === "string") return hours;

  if (hours.fromTime && hours.toTime) {
    return `${hours.fromTime} - ${hours.toTime}`;
  }

  return "Hours not specified";
};

const ExchangeCardSkeleton: React.FC = () => {
  return (
    <div className="bg-white rounded-lg border border-[#DEDEDE] w-full">
      <div className="relative">
        <Skeleton height={121} className="rounded-t-lg" />
        <div className="absolute top-3 left-3 z-30">
          <Skeleton width={100} height={24} borderRadius={4} />
        </div>
        <div className="absolute top-3 right-3 z-30">
          <Skeleton width={60} height={24} borderRadius={4} />
        </div>
      </div>

      <div className="px-[18.25px] pt-[18.25px] sm:p-[18.25px]">
        <Skeleton height={19} width="70%" />
        <Skeleton height={24} width="50%" className="mt-1" />
        <Skeleton height={20} width="90%" className="mt-[12.17px]" />
        <Skeleton height={20} width="80%" className="mt-1" />
      </div>

      <div className="mt-6 flex items-center justify-between gap-[12.17px] p-[18.25px] pt-0">
        <Skeleton height={47} width="100%" borderRadius={6} />
        <Skeleton height={47} width={47} borderRadius={6} />
      </div>
    </div>
  );
};

interface ExchangeListProps {
  cityOffices?: {
    offices: ExchangeOffice[];
    totalCount: number;
    cityInfo: {
      name: string;
      totalOffices: number;
      activeOffices: number;
      verifiedOffices: number;
      featuredOffices: number;
      availableCurrencies: string[];
    };
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  } | null;
  filteredOffices?: ExchangeOffice[];
}

const ExchangeList: React.FC<ExchangeListProps> = ({
  cityOffices,
  filteredOffices,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [displayedOffices, setDisplayedOffices] = useState<ExchangeOffice[]>(
    []
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const initialBatchSize = 9;

  useEffect(() => {
    // Reset state when cityOffices prop changes
    setIsLoading(true);
    setCurrentPage(1);
    setDisplayedOffices([]);

    // Initial loading
    const timer = setTimeout(() => {
      setIsLoading(false);
      // Use filteredOffices if available, otherwise fall back to cityOffices
      const offices = filteredOffices || cityOffices?.offices || [];
      setDisplayedOffices(offices.slice(0, initialBatchSize));
      setTotalPages(cityOffices?.pagination?.totalPages || 1);
    }, 1000);

    return () => clearTimeout(timer);
  }, [cityOffices, filteredOffices]);

  const renderSkeletons = (count: number) => {
    return Array.from({ length: count }).map((_, index) => (
      <ExchangeCardSkeleton key={`skeleton-${index}`} />
    ));
  };

  const handlePageChange = async (newPage: number) => {
    if (newPage < 1 || newPage > totalPages || newPage === currentPage) return;

    setIsLoadingMore(true);

    try {
      // Extract city name from current cityOffices or use a default
      const cityName =
        cityOffices?.cityInfo?.name?.toLowerCase() || "casablanca";

      // Fetch the specific page
      const pageData = await api.fetchCityOffices({
        cityName,
        page: newPage,
        limit: initialBatchSize,
        // Reapply filters from initial fetch
        availableCurrencies: cityOffices?.offices[0]?.availableCurrencies,
        trend: cityOffices?.offices[0]?.isPopular ? "featured" : undefined,
        showOnlyOpenNow: cityOffices?.offices.every(
          (office) => office.isCurrentlyOpen
        ),
      });

      // Replace displayed offices with new page data
      setDisplayedOffices(pageData.offices || []);
      setCurrentPage(newPage);
      setTotalPages(pageData.pagination?.totalPages || totalPages);
    } catch (error) {
      console.error("Error loading page:", error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  const generatePageNumbers = (): (number | string)[] => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is 5 or less
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      if (currentPage <= 3) {
        // Show pages 1, 2, 3, 4, ..., last
        pages.push(2, 3, 4, "...", totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Show pages 1, ..., last-3, last-2, last-1, last
        pages.push(
          "...",
          totalPages - 3,
          totalPages - 2,
          totalPages - 1,
          totalPages
        );
      } else {
        // Show pages 1, ..., current-1, current, current+1, ..., last
        pages.push(
          "...",
          currentPage - 1,
          currentPage,
          currentPage + 1,
          "...",
          totalPages
        );
      }
    }

    return pages;
  };

  return (
    <div className="mt-4 mb-6 w-full">
      <div className="w-full grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {isLoading ? (
          renderSkeletons(initialBatchSize)
        ) : (!filteredOffices || filteredOffices.length === 0) &&
          (!cityOffices || cityOffices.offices.length === 0) ? (
          <div className="col-span-full flex flex-col items-center justify-center py-12">
            <div className="text-gray-400 text-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 mx-auto mb-4 text-gray-300"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M12 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <h3 className="text-xl font-medium mb-2">
                No Exchange Offices Found
              </h3>
              <p className="text-gray-500">
                There are no exchange offices available in this location.
              </p>
              <p className="text-gray-500 mt-1">
                Try searching in Casablanca where our offices are located.
              </p>
            </div>
          </div>
        ) : (
          <>
            {displayedOffices.map((office) => (
              <ExchangeCard
                key={office.id}
                id={office.id}
                name={truncateName(office.officeName || "Unnamed Office")}
                rate={office.buyRate || "N/A"}
                address={office.address || "Address not available"}
                hours={formatWorkingHours(office.todayWorkingHours)}
                images={office.images || []}
                country={office.country.name || "Morocco"}
                isPopular={office.isPopular}
                isVerified={office.isVerified} // Add isVerified
                availableCurrencies={office.availableCurrencies}
                searchCount={office.searchCount}
                distance={office.distance}
                isCurrentlyOpen={office.isCurrentlyOpen}
                slug={office.slug} // Add slug here
              />
            ))}
          </>
        )}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex items-center gap-2">
            {/* Previous Button */}
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-2 text-[#111111] cursor-pointer text-sm font-medium border border-[#DEDEDE] rounded-md bg-white hover:bg-[#F6F6F6] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              &lt;
            </button>

            {/* Page Numbers */}
            {generatePageNumbers().map((pageNum, index) => (
              <button
                key={index}
                onClick={() => {
                  if (pageNum !== "...") {
                    handlePageChange(pageNum as number);
                  }
                }}
                disabled={pageNum === "..."}
                className={`px-3 py-2 text-sm cursor-pointer font-medium border border-[#DEDEDE] rounded-md bg-white transition-colors duration-200 ${
                  pageNum === currentPage
                    ? "bg-[#20523C] text-white border-[#20523C]"
                    : pageNum === "..."
                    ? "text-[#111111] cursor-default"
                    : "text-[#111111] hover:bg-[#F6F6F6]"
                }`}
              >
                {pageNum === "..." ? "..." : String(pageNum).padStart(2, "0")}
              </button>
            ))}

            {/* Next Button */}
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-3 py-2 text-[#111111] text-sm font-medium border border-[#DEDEDE] rounded-md bg-white hover:bg-[#F6F6F6] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              &gt;
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ExchangeList;
