"use client";
import Image from "next/image";
import React, { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import ArrowLeft from "@/components/SvgIcons/ArrowLeft";
import SortDropdown from "../Filters/SortDropdown";
import FilterButton from "../Filters/FilterButton";
import SearchBar from "../SearchBar";
import MobileExchangeCardModal from "./MobileExchangeCardModal";
import Link from "next/link";
import { FilterState } from "../types";

interface MobileHeaderProps {
  onSortClick: (option: string) => void;
  onApplyFilters?: (filters: FilterState) => void;
  onClearFilters?: () => void;
}

const sortOptions = [
  "Highest to Lowest Rate",
  "Geographic proximity",
  "Currently open/closed",
];

const MobileHeader: React.FC<MobileHeaderProps> = ({
  onSortClick,
  onApplyFilters,
  onClearFilters,
}) => {
  const searchParams = useSearchParams();
  const [showSearchDrawer, setShowSearchDrawer] = useState(false);
  const [selectedSortOption, setSelectedSortOption] = useState<string>(
    sortOptions[0]
  );
  const [isSortDropdownOpen, setIsSortDropdownOpen] = useState(false);
  const [location, setLocation] = useState<string>("");
  const [showMobileModal, setShowMobileModal] = useState(false);
  const isMobile = true;

  useEffect(() => {
    const locationParam = searchParams.get("location");
    if (locationParam) {
      setLocation(locationParam);
    }

    const handleLocationChange = (event: any) => {
      const { name } = event.detail;
      setLocation(name);
    };

    window.addEventListener("userLocationChanged", handleLocationChange);
    window.addEventListener("mapLocationChanged", handleLocationChange);

    return () => {
      window.removeEventListener("userLocationChanged", handleLocationChange);
      window.removeEventListener("mapLocationChanged", handleLocationChange);
    };
  }, [searchParams]);

  const toggleSearchDrawer = () => {
    setShowSearchDrawer(!showSearchDrawer);
  };

  const handleOptionClick = (option: string) => {
    setSelectedSortOption(option);
    setIsSortDropdownOpen(false);
    onSortClick(option);
  };

  const handleApplyFilters = (filters: FilterState) => {
    if (onApplyFilters) {
      onApplyFilters(filters);
    }
  };

  const handleClearFilters = () => {
    if (onClearFilters) {
      onClearFilters();
    }
  };

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="border-b border-[#DEDEDE] pt-2 pb-4">
        <div className="mb-2 h-[46px] w-full px-5 flex items-center justify-between gap-4">
          <button className="cursor-pointer">
            <ArrowLeft />
          </button>
          <Link href="/">
            <Image src="/assets/logo.svg" alt="logo" width={143} height={31} />
          </Link>
          <button
            onClick={toggleSearchDrawer}
            className="lg:hidden block cursor-pointer"
          >
            <Image
              src="/assets/menu-black.svg"
              alt="menu"
              width={24}
              height={24}
            />
          </button>
        </div>

        <div className="h-[46px] w-full px-5 flex items-center justify-between gap-4">
          <div className="relative flex-1">
            <Image
              src="/assets/search-normal.svg"
              alt="location"
              width={18}
              height={18}
              className="absolute left-[20%] top-1/2 -translate-y-1/2"
            />
            <input
              type="text"
              placeholder="Search Location"
              value={location}
              readOnly
              onClick={toggleSearchDrawer}
              className="border border-[#DEDEDE] text-center rounded-lg h-[46px] w-full px-2.5 placeholder:text-[#585858] text-[#111111] outline-none text-[14px] font-normal leading-[20px] cursor-pointer"
            />
          </div>

          <SortDropdown
            options={sortOptions}
            selected={selectedSortOption}
            onSelect={handleOptionClick}
            open={isSortDropdownOpen}
            setOpen={setIsSortDropdownOpen}
            isMobile={isMobile}
          />

          <FilterButton
            onApplyFilters={handleApplyFilters}
            onClearFilters={handleClearFilters}
          />
        </div>
      </div>
      {showSearchDrawer && (
        <>
          <div
            className={`fixed left-0 right-0 top-0 z-50 bg-white shadow-lg transition-transform duration-500 ease-in-out ${
              showSearchDrawer ? " " : "translate-y-full"
            }`}
          >
            <div className="h-[46px] w-full flex items-center justify-between mt-2 mb-4 px-5">
              <Link href="/">
                <Image
                  src="/assets/logo.svg"
                  alt="logo"
                  width={143}
                  height={31}
                />
              </Link>
              <button onClick={toggleSearchDrawer} aria-label="Close">
                <Image
                  src="/assets/close-circle.svg"
                  alt="close"
                  width={24}
                  height={24}
                />
              </button>
            </div>
            <div className="pb-[22px]">
              <SearchBar
                setShowSearchDrawer={setShowSearchDrawer}
                onLocationChange={setLocation}
                onCheckRates={() => {
                  setShowSearchDrawer(false);
                  setTimeout(() => {
                    setShowMobileModal(true);
                  }, 300);
                }}
              />
            </div>
          </div>
          <div
            className="fixed inset-0 z-40 bg-black/40 transition-opacity duration-300"
            onClick={toggleSearchDrawer}
          />
        </>
      )}

      {showMobileModal && (
        <MobileExchangeCardModal onClose={() => setShowMobileModal(false)} />
      )}
    </Suspense>
  );
};
export default MobileHeader;
