"use client";
import Image from "next/image";
import ExchangeList from "../ExchangeList";
import React, { useRef, useState, useCallback, useEffect } from "react";
import { HoverProvider } from "@/context/HoverContext";

interface MobileExchangeCardModalProps {
  onClose: () => void;
  resultCount?: number;
  cityOffices?: {
    cityInfo: {
      totalOffices: number;
    };
  } | null;
}

const MobileExchangeCardModal: React.FC<MobileExchangeCardModalProps> = ({
  onClose,
  resultCount,
  cityOffices,
}) => {
  const MIN_HEIGHT = 120;
  const MAX_HEIGHT = 500;
  const [height, setHeight] = useState(MIN_HEIGHT);
  const [isDragging, setIsDragging] = useState(false);
  const startYRef = useRef<number | null>(null);
  const startHeightRef = useRef<number>(MIN_HEIGHT);
  const finalHeightRef = useRef(height);
  const [city, setCity] = useState("Rabat");
  const [lastUpdate, setLastUpdate] = useState("just now");
  const lastUpdateTimeRef = useRef(Date.now());
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const handleDragMove = useCallback(
    (e: TouchEvent | MouseEvent) => {
      if (startYRef.current === null) return;

      const clientY =
        "touches" in e
          ? (e as TouchEvent).touches[0].clientY
          : (e as MouseEvent).clientY;
      const delta = startYRef.current - clientY;
      let newHeight = startHeightRef.current + delta;

      newHeight = Math.max(MIN_HEIGHT - 80, Math.min(MAX_HEIGHT, newHeight));
      setHeight(newHeight);
      finalHeightRef.current = newHeight;
    },
    [MAX_HEIGHT, MIN_HEIGHT]
  );

  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
    startYRef.current = null;
    document.body.style.userSelect = "";

    const finalHeight = finalHeightRef.current;

    if (finalHeight < MIN_HEIGHT) {
      onClose();
    } else {
      setHeight(
        finalHeight > (MIN_HEIGHT + MAX_HEIGHT) / 2 ? MAX_HEIGHT : MIN_HEIGHT
      );
    }

    window.removeEventListener("mousemove", handleDragMove);
    window.removeEventListener("mouseup", handleDragEnd);
    window.removeEventListener("touchmove", handleDragMove);
    window.removeEventListener("touchend", handleDragEnd);
  }, [handleDragMove, onClose, MIN_HEIGHT, MAX_HEIGHT]);

  const handleDragStart = useCallback(
    (e: React.TouchEvent | React.MouseEvent) => {
      setIsDragging(true);
      if ("touches" in e) {
        startYRef.current = e.touches[0].clientY;
      } else {
        startYRef.current = e.clientY;
      }
      startHeightRef.current = height;
      document.body.style.userSelect = "none";

      if ("touches" in e) {
        window.addEventListener("touchmove", handleDragMove);
        window.addEventListener("touchend", handleDragEnd);
      } else {
        window.addEventListener("mousemove", handleDragMove);
        window.addEventListener("mouseup", handleDragEnd);
      }
    },
    [height, handleDragMove, handleDragEnd]
  );

  useEffect(() => {
    const handleUserLocationChanged = (event: any) => {
      if (event.detail && event.detail.name) {
        setCity(event.detail.name);
        setLastUpdate("just now");
        lastUpdateTimeRef.current = Date.now();
      }
    };
    window.addEventListener("userLocationChanged", handleUserLocationChanged);
    return () =>
      window.removeEventListener(
        "userLocationChanged",
        handleUserLocationChanged
      );
  }, []);

  useEffect(() => {
    if (timerRef.current) clearInterval(timerRef.current);
    timerRef.current = setInterval(() => {
      const diff = Math.floor((Date.now() - lastUpdateTimeRef.current) / 60000);
      if (diff === 0) setLastUpdate("just now");
      else if (diff === 1) setLastUpdate("1 min ago");
      else setLastUpdate(`${diff} min ago`);
    }, 1000 * 10); // update every 10 seconds for demo, use 60000 for 1 min
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, []);

  useEffect(() => {
    return () => {
      window.removeEventListener("mousemove", handleDragMove);
      window.removeEventListener("mouseup", handleDragEnd);
      window.removeEventListener("touchmove", handleDragMove);
      window.removeEventListener("touchend", handleDragEnd);
    };
  }, [handleDragMove, handleDragEnd]);

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-end justify-center bg-black/40"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-t-lg shadow-lg w-full max-w-md mx-auto lg:hidden flex flex-col"
        style={{
          maxHeight: MAX_HEIGHT,
          height: height,
          transition: isDragging ? "none" : "height 0.3s ease-out",
        }}
        onClick={(e) => e.stopPropagation()}
        onMouseDown={handleDragStart}
        onTouchStart={handleDragStart}
      >
        {/* <div
          className="w-full mx-auto max-w-[42px] h-[5px] bg-[#E3E3E3] rounded-full p-0 m-0 cursor-ns-resize"
        /> */}
        <div className="px-5">
          <div className="mb-[19px] mt-2">
            <div
              className="w-full mx-auto max-w-[42px] h-[5px] bg-[#E3E3E3] rounded-full p-0 m-0 cursor-ns-resize"
              onMouseDown={handleDragStart}
              onTouchStart={handleDragStart}
            />
          </div>
          <div className="w-full flex items-center justify-between gap-3 pb-4">
            <div>
              <p className="text-[#585858] text-[11px] sm:text-[14px] font-normal leading-[15px] sm:leading-[20px]">
                Showing{" "}
                <span className="text-[#20523C] font-bold">
                  {cityOffices?.cityInfo?.totalOffices || resultCount || 0}
                </span>{" "}
                Exchange office listing in{" "}
                <span className="text-[#20523C] font-bold">{city}</span>{" "}
              </p>
              <h3 className="text-[#585858] text-[11px] sm:text-[14px] font-normal leading-[15px] sm:leading-[20px] mt-1">
                Last update{" "}
                <span className="text-[#20523C] font-bold">{lastUpdate}</span>
              </h3>
            </div>
            <button className="cursor-pointer">
              <Image
                src="/assets/whatsapp-mobile-alert.svg"
                alt="whatsapp"
                width={24.33}
                height={24.33}
              />
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto px-5">
          <HoverProvider>
            <ExchangeList />
          </HoverProvider>
        </div>
      </div>
    </div>
  );
};

export default MobileExchangeCardModal;
