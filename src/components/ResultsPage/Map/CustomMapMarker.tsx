"use client";

import type React from "react";
import { useState, useEffect } from "react";
import Image from "next/image";

interface CustomMapMarkerProps {
  rate: string;
  imageSrc: string;
  isSelected?: boolean;
}

const CustomMapMarker: React.FC<CustomMapMarkerProps> = ({
  rate,
  imageSrc,
  isSelected = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Combine the local hover state with the isSelected prop that will be driven by the context
  const isHighlighted = isHovered || isSelected;

  // Apply animation effect when selected state changes
  useEffect(() => {
    const markerElement = document.getElementById(`marker-${rate}`);
    if (markerElement && isSelected) {
      markerElement.classList.add("marker-bounce");
      setTimeout(() => {
        markerElement.classList.remove("marker-bounce");
      }, 800);
    }
  }, [isSelected, rate]);

  return (
    <div
      id={`marker-${rate}`}
      className={`relative flex flex-col items-center justify-center group cursor-pointer ${
        isSelected ? "z-10" : "z-1"
      }`}
    >
      <div
        className={`relative rounded-full shadow-md flex items-center justify-center w-[44px] h-[44px] transition-all ${
          isSelected ? "scale-110" : ""
        }`}
        style={{
          background: isHighlighted ? "#3BEE5C" : "white",
          border: `3px solid ${isHighlighted ? "#3BEE5C" : "white"}`,
          boxShadow: `inset 0 0 0 2px ${
            isHighlighted ? "white" : "#e5e7eb"
          }, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)`,
          transform: isSelected ? "scale(1.1)" : "scale(1)",
        }}
      >
        <div
          className="absolute inset-0 flex items-center justify-center cursor-pointer transition-transform duration-200"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="relative w-5 h-5">
            <Image
              src={isHighlighted ? "/assets/X-white-logo.svg" : imageSrc}
              alt="Exchange office"
              width={20}
              height={20}
              className="object-contain transition-all duration-200"
            />
          </div>
        </div>
      </div>

      <div className="absolute -top-14 flex flex-col items-center">
        <div
          className={`px-3 py-2 ${
            isHighlighted ? "bg-[#333] text-white" : "bg-white text-[#333]"
          } rounded-lg shadow-lg text-sm font-medium transition-all ${
            isHighlighted ? "scale-110 shadow-xl" : ""
          } border ${isHighlighted ? "border-[#333]" : "border-gray-200"}`}
        >
          RP{rate}
        </div>
        <div
          className={`w-0 h-0 border-l-[8px] border-r-[8px] border-t-[10px] border-l-transparent border-r-transparent ${
            isHighlighted ? "border-t-[#333]" : "border-t-white"
          } transition-all`}
          style={{
            filter: isHighlighted
              ? "drop-shadow(0 2px 4px rgba(0,0,0,0.1))"
              : "drop-shadow(0 1px 2px rgba(0,0,0,0.1))",
          }}
        />
      </div>

      <style jsx>{`
        @keyframes bounce {
          0%,
          100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        .marker-bounce {
          animation: bounce 0.8s ease;
        }
      `}</style>
    </div>
  );
};

export default CustomMapMarker;
