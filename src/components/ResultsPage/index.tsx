"use client";

import type React from "react";
import { useState, useEffect, Suspense } from "react";
import SearchBar from "./SearchBar";
import ExchangeList from "./ExchangeList";
import MapView from "./Map/MapView";
import Filters from "./Filters/Filters";
import ResultHeader from "../Phase1/ResultHeader";
import MobileHeader from "./MobileDesign/MobileHeader";
import MobileExchangeCardModal from "./MobileDesign/MobileExchangeCardModal";
import { useSearchParams } from "next/navigation";
import { HoverProvider } from "@/context/HoverContext";
import api from "@/services/api";
import { ExchangeOffice } from "./types";

// Define filter state interface
interface FilterState {
  selectedCurrencies: string[];
  selectedTrends: string[];
  showOpenOfficesOnly: boolean;
}

export const ResultsPage: React.FC = () => {
  const params = useSearchParams();
  const initialLocation = params.get("location");

  const [location, setLocation] = useState(
    initialLocation ? initialLocation.toLowerCase() : "casablanca"
  );
  const [filteredOffices, setFilteredOffices] = useState<ExchangeOffice[]>([]);
  const [count, setCount] = useState(0);
  const [lastUpdate, setLastUpdate] = useState("3 days Ago");
  const [isMapFullscreen, setIsMapFullscreen] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [sortOption, setSortOption] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterState>({
    selectedCurrencies: [],
    selectedTrends: [],
    showOpenOfficesOnly: false,
  });
  const [cityOffices, setCityOffices] = useState<{
    offices: ExchangeOffice[];
    totalCount: number;
    cityInfo: {
      name: string;
      totalOffices: number;
      activeOffices: number;
      verifiedOffices: number;
      featuredOffices: number;
      availableCurrencies: string[];
    };
    pagination: {
      page: number;
      limit: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  } | null>(null);

  // Update filtered offices and count when location changes or filters are applied
  useEffect(() => {
    // Get base offices from API data or use empty array
    let offices = cityOffices?.offices ? [...cityOffices.offices] : [];

    // Apply filters
    if (filters.selectedCurrencies.length > 0) {
      // Filter by available currencies
      offices = offices.filter((office) => {
        if (!office.availableCurrencies) return false;
        return filters.selectedCurrencies.some((currency) =>
          office.availableCurrencies?.includes(currency)
        );
      });
    }

    if (filters.selectedTrends.includes("popular")) {
      offices = offices.filter((office) => office.isPopular === true);
    }

    if (filters.selectedTrends.includes("searched")) {
      // Sort by search count and take top 5
      offices = [...offices]
        .sort((a, b) => (b.searchCount || 0) - (a.searchCount || 0))
        .slice(0, 5);
    }

    if (filters.selectedTrends.includes("nearest")) {
      // Sort by distance and take closest 7
      offices = [...offices]
        .sort((a, b) => (a.distance || 999) - (b.distance || 999))
        .slice(0, 7);
    }

    if (filters.showOpenOfficesOnly) {
      // Filter for currently open offices
      offices = offices.filter((office) => office.isCurrentlyOpen === true);
    }

    // Apply sorting if a sort option is selected
    if (sortOption) {
      offices = sortOffices(offices, sortOption);
    }

    setFilteredOffices(offices);
    setCount(offices.length);
  }, [cityOffices, sortOption, filters]);

  useEffect(() => {
    // Update cityOffices when location changes
    if (location) {
      const fetchCityOfficesData = async () => {
        try {
          const data = await api.fetchCityOffices({
            cityName: location.toLowerCase(),
            baseCurrency: "EUR", // Default base currency
            targetCurrency: "MAD", // Default target currency
            isActive: false,
            // Apply filters from state
            availableCurrencies:
              filters.selectedCurrencies.length > 0
                ? filters.selectedCurrencies
                : undefined,
            trend:
              filters.selectedTrends.length > 0
                ? (filters.selectedTrends[0] as
                    | "featured"
                    | "verified"
                    | "newest")
                : undefined,
            showOnlyOpenNow: filters.showOpenOfficesOnly,
          });
          setCityOffices(data);

          // Update count based on filtered results
          setCount(data.totalCount || data.offices.length || 0);
        } catch (error) {
          console.error("Error fetching city offices:", error);
          setCityOffices(null);
          setCount(0);
        }
      };

      fetchCityOfficesData();
    }
  }, [location, filters]);

  // Function to sort offices based on the selected option
  const sortOffices = (offices: typeof filteredOffices, option: string) => {
    const sortedOffices = [...offices];

    if (option === "Highest to Lowest Rate") {
      sortedOffices.sort((a, b) => {
        const rateA = parseFloat(a.buyRate || "0");
        const rateB = parseFloat(b.buyRate || "0");
        return rateB - rateA; // Descending order
      });
    } else if (option === "Geographic proximity") {
      // Sort by distance
      sortedOffices.sort((a, b) => (a.distance || 999) - (b.distance || 999));
    } else if (option === "Currently open/closed") {
      // Sort by open status - open offices first
      sortedOffices.sort((a, b) => {
        if (a.isCurrentlyOpen && !b.isCurrentlyOpen) return -1;
        if (!a.isCurrentlyOpen && b.isCurrentlyOpen) return 1;
        return 0;
      });
    }

    return sortedOffices;
  };

  const handleCheckRates = () => {
    // This function will be called when the Check Rates button is clicked
    // The location update will be handled by the mapLocationChanged event
    console.log("Check Rates clicked - location updates will be applied now");
  };

  const handleSort = (option: string) => {
    console.log("Sort option selected:", option);
    setSortOption(option);
  };

  const handleApplyFilters = (newFilters: FilterState) => {
    console.log("Filters applied:", newFilters);
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({
      selectedCurrencies: [],
      selectedTrends: [],
      showOpenOfficesOnly: false,
    });
  };

  useEffect(() => {
    const handleMapFullscreenToggle = (e: CustomEvent) => {
      setIsMapFullscreen(e.detail.isFullscreen);
    };

    const handleMapLocationChanged = (e: CustomEvent) => {
      if (e.detail && e.detail.name) {
        setLocation(e.detail.name);
      }
    };

    window.addEventListener(
      "mapFullscreenToggle",
      handleMapFullscreenToggle as EventListener
    );

    window.addEventListener(
      "mapLocationChanged",
      handleMapLocationChanged as EventListener
    );

    return () => {
      window.removeEventListener(
        "mapFullscreenToggle",
        handleMapFullscreenToggle as EventListener
      );
      window.removeEventListener(
        "mapLocationChanged",
        handleMapLocationChanged as EventListener
      );
    };
  }, []);

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <div className="w-full h-screen flex flex-col">
        <div className="lg:block hidden flex-shrink-0 border-b border-[#DEDEDE]">
          <ResultHeader />
        </div>
        <div className="w-full lg:hidden block">
          <MobileHeader
            onSortClick={handleSort}
            onApplyFilters={handleApplyFilters}
            onClearFilters={handleClearFilters}
          />
        </div>

        <HoverProvider>
          <div className="flex overflow-hidden w-full relative h-[calc(100vh-125px)] lg:h-screen">
            {/* left side */}
            <div
              className={` lg:block hidden overflow-y-auto hide-scrollbar pt-6 transition-all duration-300 ${
                isMapFullscreen ? "w-[0px]" : "w-full"
              }`}
            >
              <SearchBar
                setShowSearchDrawer={() => {}}
                onLocationChange={setLocation}
                onCheckRates={handleCheckRates}
              />
              <div className="w-full h-[1px] bg-[#DEDEDE] my-6"></div>
              <div className="w-full px-8 max-w-[943px] mx-auto">
                <Filters
                  count={count}
                  cityOffices={cityOffices}
                  onApplyFilters={handleApplyFilters}
                  onClearFilters={handleClearFilters}
                  location={location}
                  lastUpdate={lastUpdate}
                />
                <ExchangeList cityOffices={cityOffices} filteredOffices={filteredOffices} />
              </div>
            </div>

            {/* right side */}
            <div
              className={`${
                isMapFullscreen
                  ? "w-screen md:w-[450px] lg:w-[500px]"
                  : "w-screen lg:w-[450px] xl:w-[497px]"
              } h-full flex-shrink-0 border-l border-[#DEDEDE] transition-all duration-300`}
            >
              <div className="h-full ">
                <MapView
                  filteredOffices={filteredOffices}
                />
              </div>
            </div>
          </div>
        </HoverProvider>

        {showModal && (
          <MobileExchangeCardModal
            onClose={() => setShowModal(false)}
            resultCount={count}
            cityOffices={cityOffices}
          />
        )}
      </div>
    </Suspense>
  );
};
