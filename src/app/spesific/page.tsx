"use client";
import React, { Suspense, useEffect, useState, useRef } from "react";
import BestDealsAreWaiting from "@/components/BestDealsAreWaiting";
import Footer from "@/components/Footer";
import ExchangeList from "@/components/ResultsPage/ExchangeList";
import CurrencyExchangeRabat from "@/components/Spesific/CurrencyExchangeRabat";
import FAQSpesific from "@/components/Spesific/FAQSpesific";
import FilterSpesific from "@/components/Spesific/FilterSpesific";
import Hero from "@/components/Spesific/Hero";
import Why from "@/components/Spesific/Why";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import SortDropdown from "@/components/ResultsPage/Filters/SortDropdown";
import WhatsAppAlertModal from "@/components/ResultsPage/WhatsappAlert/WhatsAppAlertModal";
import { HoverProvider } from "@/context/HoverContext";

function SpesificContent() {
  const [isSortDropdownOpen, setIsSortDropdownOpen] = useState(false);

  // Desktop Dropdown State and Refs
  const [isDesktopDropdownOpen, setIsDesktopDropdownOpen] = useState(false);
  const desktopButtonRef = useRef<HTMLButtonElement>(null);
  const desktopDropdownRef = useRef<HTMLDivElement>(null);

  // Mobile Dropdown State and Refs
  const [isMobileDropdownOpen, setIsMobileDropdownOpen] = useState(false);
  const mobileButtonRef = useRef<HTMLButtonElement>(null);
  const mobileDropdownRef = useRef<HTMLDivElement>(null);

  const [isMobile, setIsMobile] = useState(false);
  const searchParams = useSearchParams();
  const [city, setCity] = useState(searchParams.get("city") || "Rabat");
  const [resultCount, setResultCount] = useState(0);
  const [lastUpdate, setLastUpdate] = useState("just now");
  const lastUpdateTimeRef = useRef(Date.now());
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [selectedSortOption, setSelectedSortOption] = useState<string | null>(
    null
  );
  const dropdownRef = useRef<HTMLDivElement | null>(null);
  const sortButtonRef = useRef<HTMLButtonElement | null>(null);

  const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);

  const toggleAlertModal = () => {
    setIsAlertModalOpen(!isAlertModalOpen);
  };

  const handleOptionClick = (option: string) => {
    setSelectedSortOption(option);
    setIsSortDropdownOpen(false);
  };

  // Click outside for Desktop Dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        desktopDropdownRef.current &&
        !desktopDropdownRef.current.contains(event.target as Node) &&
        desktopButtonRef.current &&
        !desktopButtonRef.current.contains(event.target as Node)
      ) {
        setIsDesktopDropdownOpen(false);
      }
    };
    if (isDesktopDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isDesktopDropdownOpen]);

  // Click outside for Mobile Dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        mobileDropdownRef.current &&
        !mobileDropdownRef.current.contains(event.target as Node) &&
        mobileButtonRef.current &&
        !mobileButtonRef.current.contains(event.target as Node)
      ) {
        setIsMobileDropdownOpen(false);
      }
    };
    if (isMobileDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileDropdownOpen]);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth <= 768);
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        sortButtonRef.current &&
        !sortButtonRef.current.contains(event.target as Node)
      ) {
        setIsSortDropdownOpen(false);
      }
    };
    if (isSortDropdownOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isSortDropdownOpen]);

  useEffect(() => {
    const handleUserLocationChanged = (event: any) => {
      if (event.detail && event.detail.name) {
        setCity(event.detail.name);
        setLastUpdate("just now");
        lastUpdateTimeRef.current = Date.now();
      }
    };
    window.addEventListener("mapLocationChanged", handleUserLocationChanged);
    return () =>
      window.removeEventListener(
        "mapLocationChanged",
        handleUserLocationChanged
      );
  }, []);

  useEffect(() => {
    if (timerRef.current) clearInterval(timerRef.current);
    timerRef.current = setInterval(() => {
      const diff = Math.floor((Date.now() - lastUpdateTimeRef.current) / 60000);
      if (diff === 0) setLastUpdate("just now");
      else if (diff === 1) setLastUpdate("1 min ago");
      else setLastUpdate(`${diff} min ago`);
    }, 1000 * 10);
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, []);

  return (
    <>
      <div className="w-full">
        <Hero />
        <div className="pt-[52px] pb-[30px] md:py-[100px] md:px-8 px-5">
          <div className="max-w-[1240px] mx-auto w-full flex items-start justify-between gap-6">
            <div className="sticky top-4 lg:block hidden">
              <FilterSpesific />
            </div>

            <div>
              <div className="w-full mb-6 sm:mb-8">
                <h1 className="text-[#111111] text-[24px] sm:text-[32px] font-bold leading-[29px] sm:leading-[38px] mb-4 sm:mb-2">
                  Some exchange offices in {city}
                </h1>
                {/* Mobile */}
                <div className="w-full lg:hidden flex items-center justify-between gap-3">
                  <div>
                    <p className="text-[#585858] text-[11px] sm:text-[14px] font-normal leading-[15px] sm:leading-[20px]">
                      Showing{" "}
                      <span className="text-[#20523C] font-bold">
                        {resultCount}
                      </span>{" "}
                      Exchange office listing in{" "}
                      <span className="text-[#20523C] font-bold">{city}</span>
                    </p>
                    <h3 className="text-[#585858] text-[11px] sm:text-[14px] font-normal leading-[15px] sm:leading-[20px] mt-1">
                      Last update{" "}
                      <span className="text-[#20523C] font-bold">
                        {lastUpdate}
                      </span>
                    </h3>
                  </div>
                  <div className="relative">
                    <button
                      ref={mobileButtonRef}
                      onClick={() => setIsMobileDropdownOpen((prev) => !prev)}
                      className="cursor-pointer"
                      title="Set Rate Alert"
                      type="button"
                    >
                      <Image
                        src="/assets/whatsapp-mobile-alert.svg"
                        alt="Rate Alert"
                        width={24}
                        height={24}
                      />
                    </button>
                    {isMobileDropdownOpen && (
                      <div
                        ref={mobileDropdownRef}
                        className="absolute right-0 top-[30px] z-40 bg-white rounded w-[137px] px-2 py-1.5"
                        style={{ boxShadow: "0px 0px 24px 0px #00000014" }}
                      >
                        <button
                          className="text-[#111111] text-[14px] leading-[18px] font-normal cursor-pointer"
                          onClick={() => {
                            setIsMobileDropdownOpen(false);
                            toggleAlertModal();
                          }}
                        >
                          Create a rate alert via WhatsApp
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Desktop */}
                <div className="w-full hidden lg:flex items-center justify-between gap-4">
                  <p className="text-[#585858] text-[11px] sm:text-[14px] font-normal leading-[15px] sm:leading-[20px]">
                    Showing{" "}
                    <span className="text-[#20523C] font-bold">
                      {resultCount}
                    </span>{" "}
                    Exchange office listing in{" "}
                    <span className="text-[#20523C] font-bold">{city}</span>
                  </p>
                  <div className="flex items-center justify-between gap-6">
                    <h3 className="text-[#585858] text-[11px] sm:text-[14px] font-normal leading-[15px] sm:leading-[20px] mt-1">
                      Last update{" "}
                      <span className="text-[#20523C] font-bold">
                        {lastUpdate}
                      </span>
                    </h3>
                    <div className="flex items-center gap-2">
                      {/* Sort */}
                      <div ref={dropdownRef}>
                        <SortDropdown
                          options={[
                            "Highest to Lowest Rate",
                            "Geographic proximity",
                            "Currently open/closed",
                          ]}
                          selected={selectedSortOption}
                          onSelect={handleOptionClick}
                          open={isSortDropdownOpen}
                          setOpen={setIsSortDropdownOpen}
                          isMobile={isMobile}
                        />
                      </div>
                      {/* Whatsapp */}
                      <div className="relative">
                        <button
                          ref={desktopButtonRef}
                          onClick={() =>
                            setIsDesktopDropdownOpen((prev) => !prev)
                          }
                          className="cursor-pointer border border-[#20523C] h-[46px] rounded-lg px-[11px] flex items-center gap-2 hover:bg-gray-100 transition-colors duration-300"
                          title="Set Rate Alert"
                          type="button"
                        >
                          <Image
                            src="/assets/Rate Alert 4.svg"
                            alt="Rate Alert"
                            width={24}
                            height={24}
                          />
                        </button>
                        {isDesktopDropdownOpen && (
                          <div
                            ref={desktopDropdownRef}
                            className="absolute right-0 top-[44px] z-40 bg-white rounded w-[137px] px-2 py-1.5"
                            style={{ boxShadow: "0px 0px 24px 0px #00000014" }}
                          >
                            <button
                              className="text-[#111111] text-[14px] leading-[18px] font-normal cursor-pointer"
                              onClick={() => {
                                setIsDesktopDropdownOpen(false);
                                toggleAlertModal();
                              }}
                            >
                              Create a rate alert via WhatsApp
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <ExchangeList />
            </div>
          </div>
        </div>
        <Why />
        <FAQSpesific />
        <CurrencyExchangeRabat />
        <BestDealsAreWaiting />
        <Footer />
      </div>
      {/* WhatsApp Alert Modal */}
      <WhatsAppAlertModal
        isOpen={isAlertModalOpen}
        onClose={toggleAlertModal}
        exchangeName="the selected exchange"
      />
    </>
  );
}

export default function Spesific() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <HoverProvider>
        <SpesificContent />
      </HoverProvider>
    </Suspense>
  );
}
