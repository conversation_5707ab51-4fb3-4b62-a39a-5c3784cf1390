"use client";
import React, { createContext, useContext, useState, useEffect } from "react";

interface VisibilityContextType {
  isVisible: boolean;
  setIsVisible: (value: boolean) => void;
}

const VisibilityContext = createContext<VisibilityContextType | undefined>(
  undefined
);

export const VisibilityProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isVisible, setIsVisible] = useState<boolean>(false);

  useEffect(() => {
    const savedVisibility = localStorage.getItem("officeVisibility");
    if (savedVisibility !== null) {
      setIsVisible(JSON.parse(savedVisibility));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem("officeVisibility", JSON.stringify(isVisible));
  }, [isVisible]);

  return (
    <VisibilityContext.Provider value={{ isVisible, setIsVisible }}>
      {children}
    </VisibilityContext.Provider>
  );
};

export const useVisibility = () => {
  const context = useContext(VisibilityContext);
  if (context === undefined) {
    throw new Error("useVisibility must be used within a VisibilityProvider");
  }
  return context;
};
