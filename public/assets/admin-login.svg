<svg width="1440" height="643" viewBox="0 0 1440 643" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_125_190)">
<rect x="-327.156" y="318.582" width="214.447" height="735.709" transform="rotate(-51.7842 -327.156 318.582)" fill="url(#paint0_linear_125_190)"/>
</g>
<g filter="url(#filter1_f_125_190)">
<rect x="-134.492" y="536.496" width="152.922" height="524.633" transform="rotate(-51.7842 -134.492 536.496)" fill="url(#paint1_linear_125_190)"/>
</g>
<g filter="url(#filter2_f_125_190)">
<rect width="214.447" height="735.709" transform="matrix(-0.618626 -0.785686 -0.785686 0.618626 1779.16 318.582)" fill="url(#paint2_linear_125_190)"/>
</g>
<g filter="url(#filter3_f_125_190)">
<rect width="152.922" height="524.633" transform="matrix(-0.618626 -0.785686 -0.785686 0.618626 1586.48 536.496)" fill="url(#paint3_linear_125_190)"/>
</g>
<defs>
<filter id="filter0_f_125_190" x="-477.156" y="0.09375" width="1010.7" height="923.617" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="75" result="effect1_foregroundBlur_125_190"/>
</filter>
<filter id="filter1_f_125_190" x="-264.492" y="286.348" width="766.797" height="704.699" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="65" result="effect1_foregroundBlur_125_190"/>
</filter>
<filter id="filter2_f_125_190" x="918.461" y="0.09375" width="1010.7" height="923.617" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="75" result="effect1_foregroundBlur_125_190"/>
</filter>
<filter id="filter3_f_125_190" x="949.688" y="286.348" width="766.797" height="704.699" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="65" result="effect1_foregroundBlur_125_190"/>
</filter>
<linearGradient id="paint0_linear_125_190" x1="-112.709" y1="399.389" x2="-399.248" y2="892.876" gradientUnits="userSpaceOnUse">
<stop stop-color="#C0FFCC"/>
<stop offset="1" stop-color="#D0E500"/>
</linearGradient>
<linearGradient id="paint1_linear_125_190" x1="18.4297" y1="594.12" x2="-185.901" y2="946.025" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#20523C"/>
</linearGradient>
<linearGradient id="paint2_linear_125_190" x1="214.447" y1="80.8074" x2="-72.0915" y2="574.294" gradientUnits="userSpaceOnUse">
<stop stop-color="#C0FFCC"/>
<stop offset="1" stop-color="#D0E500"/>
</linearGradient>
<linearGradient id="paint3_linear_125_190" x1="152.922" y1="57.6236" x2="-51.4084" y2="409.528" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#20523C"/>
</linearGradient>
</defs>
</svg>
