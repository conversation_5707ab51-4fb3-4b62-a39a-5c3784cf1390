<svg width="375" height="301" viewBox="0 0 375 301" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_125_190)">
<rect x="-227.5" y="183.17" width="123.016" height="422.035" transform="rotate(-51.7842 -227.5 183.17)" fill="url(#paint0_linear_125_190)"/>
</g>
<g filter="url(#filter1_f_125_190)">
<rect x="-116.969" y="308.176" width="87.7228" height="300.953" transform="rotate(-51.7842 -116.969 308.176)" fill="url(#paint1_linear_125_190)"/>
</g>
<g filter="url(#filter2_f_125_190)">
<rect width="123.016" height="422.035" transform="matrix(-0.618626 -0.785686 -0.785686 0.618626 622.125 183.17)" fill="url(#paint2_linear_125_190)"/>
</g>
<g filter="url(#filter3_f_125_190)">
<rect width="87.7228" height="300.953" transform="matrix(-0.618626 -0.785686 -0.785686 0.618626 511.594 308.176)" fill="url(#paint3_linear_125_190)"/>
</g>
<defs>
<filter id="filter0_f_125_190" x="-313.547" y="0.470924" width="579.781" height="529.828" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="43.0233" result="effect1_foregroundBlur_125_190"/>
</filter>
<filter id="filter1_f_125_190" x="-191.543" y="164.68" width="439.866" height="404.247" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="37.2869" result="effect1_foregroundBlur_125_190"/>
</filter>
<filter id="filter2_f_125_190" x="128.391" y="0.470924" width="579.781" height="529.828" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="43.0233" result="effect1_foregroundBlur_125_190"/>
</filter>
<filter id="filter3_f_125_190" x="146.301" y="164.68" width="439.866" height="404.247" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="37.2869" result="effect1_foregroundBlur_125_190"/>
</filter>
<linearGradient id="paint0_linear_125_190" x1="-104.484" y1="229.525" x2="-268.855" y2="512.611" gradientUnits="userSpaceOnUse">
<stop stop-color="#C0FFCC"/>
<stop offset="1" stop-color="#D0E500"/>
</linearGradient>
<linearGradient id="paint1_linear_125_190" x1="-29.246" y1="341.231" x2="-146.459" y2="543.099" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#20523C"/>
</linearGradient>
<linearGradient id="paint2_linear_125_190" x1="123.016" y1="46.3547" x2="-41.3549" y2="329.441" gradientUnits="userSpaceOnUse">
<stop stop-color="#C0FFCC"/>
<stop offset="1" stop-color="#D0E500"/>
</linearGradient>
<linearGradient id="paint3_linear_125_190" x1="87.7228" y1="33.0555" x2="-29.4901" y2="234.924" gradientUnits="userSpaceOnUse">
<stop stop-color="#3BEE5C"/>
<stop offset="1" stop-color="#20523C"/>
</linearGradient>
</defs>
</svg>
